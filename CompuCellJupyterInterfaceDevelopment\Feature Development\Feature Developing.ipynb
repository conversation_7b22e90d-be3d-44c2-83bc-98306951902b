{"cells": [{"cell_type": "markdown", "id": "9213401c", "metadata": {}, "source": ["# Parameters for all plugins\n", "Metadata | PottsCores | All Plugins\n", "\n", "Concept understanding: when user adjusts a value in UI, we will have feature update the values in the .spec_dict (this .spec_dict stores user instance) However, this is in-memory data meaning when the notebook is closed, the data is not persistent. \n", "\n", "General data flow: UI instance --> validate data (depends_on) if yes, --> .spec_dict (change) --> overwrite JSON --> Load Json file\n", "\n", "1. Why is debug_output_frequency appear both in PottsCore & Metadata?\n", "    - keep the one in Metadata, ignore the one for PottsCore unless it's loading legacy CC3DML file\n", "    - Or the instance in Metadata will get pass to PottsCore.spec_dict\n", "\n", "2. Issue with repeated instance not been sync. \n", "Since each instance's .spec_dict is seperated, for consistency we have to do our own UI logic to keep single source of truth and ignore one keep one. \n", "\n", "Example for sync instance:\n", "```\n", "def sync_debug_output_frequency(metadata, potts):\n", "    potts.spec_dict[\"debug_output_frequency\"] = metadata.spec_dict[\"debug_output_frequency\"]\n", "\n", "# Whenever the user changes the value in the UI:\n", "metadata.spec_dict[\"debug_output_frequency\"] = 50\n", "sync_debug_output_frequency(metadata, potts)\n", "\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "f7f8ecfe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Plugin: AdhesionFlexPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4190ea6046b046cfbde031098e08e105", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='1', description='neighbor_order', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f0a973183e9e447abef6c6c6be033936", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='[]', description='molecules', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0857c172fed741789c4875f6cb3c482c", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='densities', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5f4051d7445c4a2b89f9a3cdda671e55", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='binding_formulas', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: BoundaryPixelTrackerPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7f43d6810c8143e9828ab8c22411c49b", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='1', description='neighbor_order', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: CellTypePlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ac6cf3989bab45998bd962f4d71d9043", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value=\"[('Medium', 0, False)]\", description='cell_types', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: CenterOfMassPlugin\n", "Plugin: ChemotaxisPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e771f16cd6634c08965378746507048c", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='field_specs', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: ConnectivityGlobalPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ba01269252d14412ac2c4a845d52cb5d", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='False', description='fast', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b628904be3a841efaec19f9447ab2aef", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='[]', description='cell_types', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: ConnectivityPlugin\n", "Plugin: ContactPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "88cc4a08602e41e9a546ee5584c8db82", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='energies', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e0063ade3b254bf3b1dfee5ae7c2ab77", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='1', description='neighbor_order', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: <PERSON><PERSON><PERSON><PERSON><PERSON>lugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a90641a5b00c4cf8a2ecb1791d245aac", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='param_specs', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8bd6060c65154f31960eb1d1b9bf864a", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='type_spec', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: ExternalPotentialPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2f19d0359f35421aad102bd93607e19e", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='None', description='lambda_x', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d806db4c302840abb3a5cae0a247c56c", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='None', description='lambda_y', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "191bd1f2e00144c0bf05aa8edc0687ef", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='None', description='lambda_z', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2eb55f9078ed4050a846dc0297a98370", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='False', description='com_based', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4c39315f77cc49d3b5a5ca373588a8cf", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='param_specs', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: FocalPointPlasticityPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bb3481e61f9743a48ca0a28387c4264c", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='1', description='neighbor_order', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "100372a47bfa497dadc4c5c50dd4e9f2", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='param_specs', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: LengthConstraintPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "05d58aa9684646209ad78884e2f4e123", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='param_specs', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: MomentOfInertiaPlugin\n", "Plugin: <PERSON>eighborTrackerPlugin\n", "Plugin: PixelTrackerPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b4998bb51a9d4474a4ef60cda507168e", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='False', description='track_medium', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: SecretionPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4fa2da3846ab44679046bcb127b34b38", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='True', description='pixel_tracker', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "98e10253ceb24b36a5cea482ceadbc36", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='True', description='boundary_pixel_tracker', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4d29bda4b5c74005be89a15ecd7c6ebc", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='field_specs', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: SurfacePlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5771fc7adc5c45b8aec4d3cda28693ed", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='params', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: VolumePlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fff75029e42249848eecab6499d77078", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='params', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import ipywidgets as widgets\n", "from IPython.display import display\n", "from cc3d.core.PyCoreSpecs import PLUGINS\n", "\n", "for plugin_cls in PLUGINS:\n", "    plugin = plugin_cls()\n", "    print(f\"Plugin: {plugin_cls.__name__}\")\n", "    for param_name in plugin.spec_dict.keys():\n", "        # For demonstration, create a text box for each parameter\n", "        value = plugin.spec_dict[param_name]\n", "        input_widget = widgets.Text(\n", "            value=str(value),\n", "            description=param_name,\n", "            style={'description_width': 'initial'},\n", "        )\n", "        display(input_widget)"]}, {"cell_type": "code", "execution_count": 2, "id": "a940371e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PottsCore parameters:\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7151472805c14a3ba3f37270bdf1ec35", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='100', description='dim_x', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f8cf46e3dd754bf988c4cbb000a529f3", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='100', description='dim_y', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c647df527bf74fa89490793c3908ed41", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='1', description='dim_z', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c3abc4c38dd849bc886ed72ced387d50", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='0', description='steps', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0c1ab8eb1918447f848d3a82e80bc77b", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='0', description='anneal', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "019e8ed673534a6e8cc245bf3a216317", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='10.0', description='fluctuation_amplitude', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8a5d4f1e9d7345618d731e20e1cca76d", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='Min', description='fluctuation_amplitude_function', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0632661313c6463dbd2f9306fd48d0da", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='NoFlux', description='boundary_x', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f78af0b2e54945fab3f48286f22526bf", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='NoFlux', description='boundary_y', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dfe12d50059e44d08dbe66f0b6c8c19b", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='NoFlux', description='boundary_z', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9ebc20c6de3f430fb50b827fb5b8e16e", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='1', description='neighbor_order', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ea05269c55aa4d83934a9a4e8ba622cb", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='0', description='debug_output_frequency', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5e56c6d304aa4f33858536e85653b1ff", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='None', description='random_seed', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "13304552bf374a2cb3daf2b17bc5855f", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='Cartesian', description='lattice_type', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c8678873a759431eb2da33504e357a80", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='0', description='offset', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6319b54726e140eab8c74cd76df1e437", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='None', description='energy_function_calculator', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Metadata parameters:\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b0a59d017e4b4537a153efc9f058ca84", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='4', description='num_processors', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ad13bf9ba59b486c94b3d5c7593910f5", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='10', description='debug_output_frequency', style=TextStyle(description_width='initial'))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from cc3d.core.PyCoreSpecs import PottsCore, Metadata\n", "import ipywidgets as widgets\n", "from IPython.display import display\n", "\n", "# Create instances\n", "potts = PottsCore(dim_x=100, dim_y=100, dim_z=1)\n", "metadata = Metadata(num_processors=4, debug_output_frequency=10)\n", "\n", "# Display widgets for each parameter in PottsCore\n", "print(\"PottsCore parameters:\")\n", "for param_name, value in potts.spec_dict.items():\n", "    input_widget = widgets.Text(\n", "        value=str(value),\n", "        description=param_name,\n", "        style={'description_width': 'initial'}\n", "    )\n", "    display(input_widget)\n", "    # print(f\"{param_name}: {value}\")\n", "\n", "# Display widgets for each parameter in Metadata\n", "print(\"Metadata parameters:\")\n", "for param_name, value in metadata.spec_dict.items():\n", "    input_widget = widgets.Text(\n", "        value=str(value),\n", "        description=param_name,\n", "        style={'description_width': 'initial'}  # Adjust description width\n", "    )\n", "    display(input_widget)\n", "    # Why is debug output frequency shown on both PottsCore and Metadata?"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}