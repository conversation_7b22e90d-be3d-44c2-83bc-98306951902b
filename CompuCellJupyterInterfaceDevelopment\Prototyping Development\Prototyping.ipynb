{"cells": [{"cell_type": "markdown", "id": "df2779a3", "metadata": {}, "source": ["## Tasks\n", "1. Figure out how to connect widgets to the actual PycoreSpec. \n", "2. What are some default values? ✅\n", "3. Debugging with Met<PERSON><PERSON>? I think they have something pre-build for error check. 🤔\n", "4. Visualizing with simulationers"]}, {"cell_type": "code", "execution_count": 1, "id": "4bae527e", "metadata": {}, "outputs": [], "source": ["# Imports \n", "import os\n", "import json\n", "import ipywidgets as widgets\n", "from ipywidgets import VBox, HBox, Layout\n", "from IPython.display import display\n", "from cc3d.core.PyCoreSpecs import Metadata, PottsCore, CellTypePlugin\n", "\n", "# from cc3d import CompuCellSetup"]}, {"cell_type": "code", "execution_count": 2, "id": "bb94556f", "metadata": {}, "outputs": [], "source": ["# Data Persistency \n", "SAVE_FILE = 'simutation_setup.json'\n", "\n", "# Default values\n", "defaults = {\n", "    \"Metadata\": {\n", "        \"num_processors\": 4,\n", "        \"debug_output_frequency\": 10\n", "    },\n", "    \"PottsCore\": {\n", "        \"dim_x\": 101,\n", "        \"dim_y\": 101,\n", "        \"dim_z\": 1,\n", "        \"steps\": 100000,\n", "        \"anneal\": 0,\n", "        \"fluctuation_amplitude\": 10.0,\n", "        \"fluctuation_amplitude_function\": \"Min\",\n", "        \"boundary_x\": \"NoFlux\",\n", "        \"boundary_y\": \"NoFlux\",\n", "        \"boundary_z\": \"NoFlux\",\n", "        \"neighbor_order\": 1,\n", "        \"random_seed\": None,\n", "        \"lattice_type\": \"Cartesian\",\n", "        \"offset\": 0\n", "    },\n", "    \"CellType\": [\n", "        {\"Cell type\": \"Medium\", \"freeze\": False}\n", "    ]\n", "}\n", "\n", "# Load saved values or use defaults\n", "if os.path.exists(SAVE_FILE):\n", "    with open(SAVE_FILE, 'r') as f:\n", "        saved_values = json.load(f)\n", "else:\n", "    saved_values = defaults.copy()\n", "\n", "def save_to_json(*args):\n", "    with open(SAVE_FILE, 'w') as f:\n", "        json.dump(current_config, f, indent=4)\n", "\n", "def current_config():\n", "    return {\n", "        \"Metadata\": {\n", "            \"num_processors\": num_processors.value,\n", "            \"debug_output_frequency\": debug_output_frequency.value\n", "        },\n", "        \"PottsCore\": {\n", "            \"dim_x\": dim_x.value + 1, # Adjusting for store values\n", "            \"dim_y\": dim_y.value + 1,\n", "            \"dim_z\": dim_z.value + 1,\n", "            \"steps\": steps.value,\n", "            \"anneal\": anneal.value,\n", "            \"fluctuation_amplitude\": fluctuation_amplitude.value,\n", "            \"fluctuation_amplitude_function\": fluctuation_amplitude_function.value,\n", "            \"boundary_x\": boundary_x.value,\n", "            \"boundary_y\": boundary_y.value,\n", "            \"boundary_z\": boundary_z.value,\n", "            \"neighbor_order\": neighbor_order.value,\n", "            \"random_seed\": random_seed.value if random_seed.value else None,\n", "            \"lattice_type\": lattice_type.value,\n", "            \"offset\": offset.value\n", "        },\n", "        \"CellType\": {\n", "            \"Type\": cell_type.value,\n", "            \"Freeze\": freeze_cell_type.value\n", "        }\n", "    }"]}, {"cell_type": "markdown", "id": "1d07f82f", "metadata": {}, "source": ["# Widgets"]}, {"cell_type": "code", "execution_count": 3, "id": "6d2145bb", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax. Perhaps you forgot a comma? (2304202667.py, line 10)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[3], line 10\u001b[1;36m\u001b[0m\n\u001b[1;33m    description='Debug Output Frequency:'\u001b[0m\n\u001b[1;37m                ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m invalid syntax. Perhaps you forgot a comma?\n"]}], "source": ["\n", "# Metadata\n", "num_proc = widgets.IntText(\n", "    value=saved_values[\"Metadata\"][\"num_processors\"],\n", "    description='Number of Processors:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "debug_freq = widgets.IntText(\n", "    value=saved_values[\"Metadata\"][\"debug_output_frequency\"],\n", "    description='Debug Output Frequency:'\n", "    style={'description_width': 'initial'},\n", ")\n", "\n", "# PottsCore \n", "x_slider = widgets.IntSlider(\n", "    value=saved_values[\"PottsCore\"][\"dim_x\"] - 1,  min=0, max=100,\n", "    description='X:'\n", ")\n", "\n", "y_slider = widgets.IntSlider(\n", "    value=saved_values[\"PottsCore\"][\"dim_y\"] - 1,  min=0, max=100,\n", "    description='Y:'\n", ")\n", "\n", "z_slider = widgets.IntSlider(\n", "    value=saved_values[\"PottsCore\"][\"dim_z\"] - 1,  min= 0, max=100,\n", "    description='Z:'\n", ")\n", "\n", "steps_input = widgets.IntText(\n", "    value=saved_values[\"PottsCore\"][\"steps\"],\n", "    description='MC Steps:'\n", ")\n", "\n", "anneal_input = widgets.FloatText(\n", "    value=saved_values[\"PottsCore\"][\"anneal\"],\n", "    description='Anneal:'\n", ")\n", "\n", "flunctuation_slider = widgets.FloatSlider(\n", "    value=saved_values[\"PottsCore\"][\"fluctuation_amplitude\"],\n", "    min=0.0, max=50.0, step=0.1, # what's the max value?\n", "    description='Fluctuation:'\n", ")\n", "\n", "flunct_fn_dropdown = widgets.Dropdown(\n", "    options=['<PERSON>', '<PERSON>', 'ArithmeticAverage'],\n", "    value=saved_values[\"PottsCore\"][\"fluctuation_amplitude_function\"],\n", "    description='Fluctuation Function:'\n", ")\n", "\n", "boundary_x = widgets.Dropdown(\n", "    options=['NoFlux', 'Periodic'],\n", "    value=saved_values[\"PottsCore\"][\"boundary_x\"],\n", "    description='Boundary X:'\n", ")\n", "\n", "boundary_y = widgets.Dropdown(\n", "    options=['NoFlux', 'Periodic'],\n", "    value=saved_values[\"PottsCore\"][\"boundary_y\"],\n", "    description='Boundary Y:'\n", ")\n", "\n", "boundary_z = widgets.Dropdown(\n", "    options=['NoFlux', 'Periodic'],\n", "    value=saved_values[\"PottsCore\"][\"boundary_z\"],\n", "    description='Boundary Z:'\n", ")\n", "\n", "neighbor_order_input = widgets.BoundedIntText(\n", "    value=saved_values[\"PottsCore\"][\"neighbor_order\"], min=1, max=4, # max?\n", "    description='Neighbor Order:'\n", ")\n", "\n", "seed_input = widgets.Text(\n", "    value='' if saved_values[\"PottsCore\"][\"random_seed\"] is None else str(saved_values[\"PottsCore\"][\"random_seed\"]),\n", "    description='Random Seed:'\n", ")\n", "\n", "lattice_dropdown = widgets.Dropdown(\n", "    options=['Square', 'Hexagonal'],\n", "    value='Square' if saved_values[\"PottsCore\"][\"lattice_type\"] == 'Cartesian' else 'Hexagonal',\n", "    description='Lattice Type:'\n", ")\n", "\n", "offset_input = widgets.IntText(\n", "    value=saved_values[\"PottsCore\"][\"offset\"],\n", "    description='Offset:'\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1cbf47ae", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "list indices must be integers or slices, not str", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[10], line 5\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Cell Type\u001b[39;00m\n\u001b[1;32m      3\u001b[0m cell_type_radio \u001b[38;5;241m=\u001b[39m widgets\u001b[38;5;241m.\u001b[39mRadioButtons(\n\u001b[1;32m      4\u001b[0m     options\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mMedium\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mCondensing\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mNonCondensing\u001b[39m\u001b[38;5;124m'\u001b[39m],  \u001b[38;5;66;03m# Add more\u001b[39;00m\n\u001b[0;32m----> 5\u001b[0m     value\u001b[38;5;241m=\u001b[39m\u001b[43msaved_values\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mCellType\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mType\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m,\n\u001b[1;32m      6\u001b[0m     description\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mCell Type:\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m      7\u001b[0m )\n\u001b[1;32m      9\u001b[0m freeze_cell_type_checkbox \u001b[38;5;241m=\u001b[39m widgets\u001b[38;5;241m.\u001b[39mCheckbox(\n\u001b[1;32m     10\u001b[0m     value\u001b[38;5;241m=\u001b[39msaved_values[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCellType\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFreeze\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[1;32m     11\u001b[0m     description\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mFreeze\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m     12\u001b[0m )\n", "\u001b[0;31mTypeError\u001b[0m: list indices must be integers or slices, not str"]}], "source": ["# Cell Type\n", "\n", "preset_dropdown = widgets.Dropdown(\n", "    options=[\"Condensing\", \"NonCondensing\", \"Customize\"],\n", "    value=\"Condensing\",\n", "    description=\"Cell Type:\",\n", "    layout=Layout(width='250px', min_width='250px')\n", ")\n", "\n", "freeze_checkbox = widgets.Checkbox(\n", "    value=True,\n", "    description=\"Freeze\",\n", "    layout=Layout(margin='0 0 0 12px', width='120px'),\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "custom_name_input = widgets.Text(\n", "    description=\"Name:\",\n", "    placeholder=\"e.g. T1\",\n", "    layout=Layout(width='250px', min_width='250px')\n", ")\n", "\n", "add_button = widgets.Button(\n", "    description=\"Add\",\n", "    button_style=\"success\",\n", "    layout=Layout(margin='10px 0 0 0', width='80px')\n", ")\n", "\n", "celltype_display_box = widgets.VBox(\n", "    layout=Layout(\n", "        border='1px solid gray',\n", "        padding='10px',\n", "        width='300px',\n", "        height='auto',\n", "        overflow_y='auto'\n", "    )\n", ")\n", "\n", "def toggle_custom_input(change):\n", "    custom_name_input.layout.display = 'block' if change['new'] == \"Customize\" else 'none'\n", "\n", "preset_dropdown.observe(toggle_custom_input, names='value')\n", "toggle_custom_input({'new': preset_dropdown.value})\n", "\n", "def update_celltype_display():\n", "    items = []\n", "    for i, entry in enumerate(celltype_entries):\n", "        label_str = entry[\"Cell type\"]\n", "        if entry[\"freeze\"]:\n", "            label_str += \" (frozen)\"\n", "        label = widgets.Label(label_str, layout=Layout(flex='1 1 auto'))\n", "        remove_btn = widgets.Button(description=\"Remove\", button_style='danger', layout=Layout(width='80px'))\n", "        def remove_closure(index=i):\n", "            def remove_clicked(b):\n", "                del celltype_entries[index]\n", "                update_celltype_display()\n", "            return remove_clicked\n", "        remove_btn.on_click(remove_closure())\n", "        item_row = HBox([label, remove_btn], layout=Layout(justify_content='space-between'))\n", "        items.append(item_row)\n", "    celltype_display_box.children = items\n", "\n", "def on_add_clicked(b):\n", "    selected = preset_dropdown.value\n", "    if selected == \"Customize\":\n", "        name = custom_name_input.value.strip()\n", "        if not name:\n", "            custom_name_input.placeholder = \"Please enter a name!\"\n", "            return\n", "    else:\n", "        name = selected\n", "    freeze = freeze_checkbox.value\n", "    celltype_entries.append({\"Cell type\": name, \"freeze\": freeze})\n", "    update_celltype_display()\n", "    custom_name_input.value = \"\"\n", "    freeze_checkbox.value = True\n", "\n", "add_button.on_click(on_add_clicked)\n", "\n", "custom_name_input.layout.display = 'none'\n", "\n", "# Labels for columns (like form labels)\n", "label_current_types = widgets.Label(\"Current Cell Types\", layout=Layout(padding='0 0 8px 0'))\n", "label_add_type = widgets.Label(\"Add Cell Type\", layout=Layout(padding='0 0 8px 0'))\n", "\n", "left_column = VBox([\n", "    label_current_types,\n", "    celltype_display_box\n", "], layout=Layout(width='300px', padding='0 40px 0 0'))\n", "\n", "right_column = VBox([\n", "    label_add_type,\n", "    preset_dropdown,\n", "    custom_name_input,\n", "    freeze_checkbox,\n", "    add_button\n", "], layout=Layout(width='300px', padding='0 0 0 40px'))\n", "\n", "celltype_section = VBox([\n", "    HBox([left_column, right_column], layout=Layout(justify_content='flex-start', gap='40px'))\n", "])"]}, {"cell_type": "code", "execution_count": null, "id": "19e566c6", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'dim_x' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[32], line 11\u001b[0m, in \u001b[0;36mreset_to_defaults\u001b[0;34m(btn)\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m key, val \u001b[38;5;129;01min\u001b[39;00m defaults[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPottsCore\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_x\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 11\u001b[0m         \u001b[43mdim_x\u001b[49m\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m  \u001b[38;5;66;03m# Adjusting for store values\u001b[39;00m\n\u001b[1;32m     12\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_y\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m     13\u001b[0m         dim_y\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "\u001b[0;31mNameError\u001b[0m: name 'dim_x' is not defined"]}, {"ename": "NameError", "evalue": "name 'dim_x' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[32], line 11\u001b[0m, in \u001b[0;36mreset_to_defaults\u001b[0;34m(btn)\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m key, val \u001b[38;5;129;01min\u001b[39;00m defaults[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPottsCore\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_x\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 11\u001b[0m         \u001b[43mdim_x\u001b[49m\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m  \u001b[38;5;66;03m# Adjusting for store values\u001b[39;00m\n\u001b[1;32m     12\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_y\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m     13\u001b[0m         dim_y\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "\u001b[0;31mNameError\u001b[0m: name 'dim_x' is not defined"]}, {"ename": "NameError", "evalue": "name 'dim_x' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[32], line 11\u001b[0m, in \u001b[0;36mreset_to_defaults\u001b[0;34m(btn)\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m key, val \u001b[38;5;129;01min\u001b[39;00m defaults[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPottsCore\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_x\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 11\u001b[0m         \u001b[43mdim_x\u001b[49m\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m  \u001b[38;5;66;03m# Adjusting for store values\u001b[39;00m\n\u001b[1;32m     12\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_y\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m     13\u001b[0m         dim_y\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "\u001b[0;31mNameError\u001b[0m: name 'dim_x' is not defined"]}, {"ename": "NameError", "evalue": "name 'dim_x' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[32], line 11\u001b[0m, in \u001b[0;36mreset_to_defaults\u001b[0;34m(btn)\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m key, val \u001b[38;5;129;01min\u001b[39;00m defaults[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPottsCore\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_x\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 11\u001b[0m         \u001b[43mdim_x\u001b[49m\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m  \u001b[38;5;66;03m# Adjusting for store values\u001b[39;00m\n\u001b[1;32m     12\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_y\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m     13\u001b[0m         dim_y\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "\u001b[0;31mNameError\u001b[0m: name 'dim_x' is not defined"]}, {"ename": "NameError", "evalue": "name 'dim_x' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[32], line 11\u001b[0m, in \u001b[0;36mreset_to_defaults\u001b[0;34m(btn)\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m key, val \u001b[38;5;129;01min\u001b[39;00m defaults[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPottsCore\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_x\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 11\u001b[0m         \u001b[43mdim_x\u001b[49m\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m  \u001b[38;5;66;03m# Adjusting for store values\u001b[39;00m\n\u001b[1;32m     12\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_y\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m     13\u001b[0m         dim_y\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "\u001b[0;31mNameError\u001b[0m: name 'dim_x' is not defined"]}, {"ename": "NameError", "evalue": "name 'dim_x' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[32], line 11\u001b[0m, in \u001b[0;36mreset_to_defaults\u001b[0;34m(btn)\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m key, val \u001b[38;5;129;01min\u001b[39;00m defaults[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPottsCore\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_x\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 11\u001b[0m         \u001b[43mdim_x\u001b[49m\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m  \u001b[38;5;66;03m# Adjusting for store values\u001b[39;00m\n\u001b[1;32m     12\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_y\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m     13\u001b[0m         dim_y\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "\u001b[0;31mNameError\u001b[0m: name 'dim_x' is not defined"]}, {"ename": "NameError", "evalue": "name 'dim_x' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[32], line 11\u001b[0m, in \u001b[0;36mreset_to_defaults\u001b[0;34m(btn)\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m key, val \u001b[38;5;129;01min\u001b[39;00m defaults[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPottsCore\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_x\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 11\u001b[0m         \u001b[43mdim_x\u001b[49m\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m  \u001b[38;5;66;03m# Adjusting for store values\u001b[39;00m\n\u001b[1;32m     12\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m key \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdim_y\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m     13\u001b[0m         dim_y\u001b[38;5;241m.\u001b[39mvalue \u001b[38;5;241m=\u001b[39m val \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "\u001b[0;31mNameError\u001b[0m: name 'dim_x' is not defined"]}], "source": ["# Reset But<PERSON>\n", "reset_button = widgets.Button(description=\"Reset to Defaults\", button_style='warning')\n", "def reset_to_defaults(btn):\n", "    for key, val in defaults[\"Metadata\"].items():\n", "        if key == \"num_processors\":\n", "            num_proc.value = val\n", "        elif key == \"debug_output_frequency\":\n", "            debug_output_frequency.value = val\n", "    for key, val in defaults[\"PottsCore\"].items():\n", "        if key == \"dim_x\":\n", "            dim_x.value = val - 1  # Adjusting for store values\n", "        elif key == \"dim_y\":\n", "            dim_y.value = val - 1\n", "        elif key == \"dim_z\":\n", "            dim_z.value = val - 1\n", "        elif key == \"steps\":\n", "            steps.value = val\n", "        elif key == \"anneal\":\n", "            anneal.value = val\n", "        elif key == \"fluctuation_amplitude\":\n", "            fluctuation_amplitude.value = val\n", "        elif key == \"fluctuation_amplitude_function\":\n", "            fluctuation_amplitude_function.value = val\n", "        elif key == \"boundary_x\":\n", "            boundary_x.value = val\n", "        elif key == \"boundary_y\":\n", "            boundary_y.value = val\n", "        elif key == \"boundary_z\":\n", "            boundary_z.value = val\n", "        elif key == \"neighbor_order\":\n", "            neighbor_order.value = val\n", "        elif key == \"random_seed\":\n", "            seed_input.value = '' if val is None else str(val)\n", "        elif key == \"lattice_type\":\n", "            lattice_dropdown.value = 'Square' if val == 'Cartesian' else 'Hexagonal'\n", "        elif key == \"offset\":\n", "            offset.value = val\n", "    cell_type_radio.value = defaults[\"CellType\"][\"Type\"]\n", "    freeze_cell_type_checkbox.value = defaults[\"CellType\"][\"Freeze\"]\n", "    save_to_json()\n", "\n", "reset_button.on_click(reset_to_defaults)\n", "\n", "# Auto save on change\n", "for widget in [\n", "    num_proc, debug_freq,\n", "    x_slider, y_slider, z_slider,\n", "    steps_input, anneal_input,\n", "    flunctuation_slider, flunct_fn_dropdown,\n", "    boundary_x, boundary_y, boundary_z,\n", "    neighbor_order_input, seed_input,\n", "    lattice_dropdown, offset_input,\n", "    cell_type_radio, freeze_cell_type_checkbox\n", "]:\n", "    widget.observe(save_to_json, 'value')"]}, {"cell_type": "code", "execution_count": null, "id": "3ec2c886", "metadata": {}, "outputs": [], "source": ["# Layout and display\n", "tab_0 = VBox([widgets.HTML(\"<h4>Basic Simulation Setup</h4>\"), num_proc, debug_freq])\n", "tab_1 = VBox([widgets.HTML(\"<h4>PottsCore Parameters</h4>\"),\n", "              x_slider, y_slider, z_slider,\n", "              steps_input, anneal_input,\n", "              flunctuation_slider, flunct_fn_dropdown,\n", "              HBox([boundary_x, boundary_y, boundary_z,]),\n", "              neighbor_order_input, seed_input,\n", "              lattice_dropdown, offset_input])\n", "tab_2 = VBox([widgets.HTML(\"<h4>Cell Type Parameters</h4>\"),\n", "              cell_type_radio, freeze_cell_type_checkbox])\n", "\n", "tabs = widgets.Tab(children=[tab_0, tab_1, tab_2])\n", "tabs.set_title(0, 'Meta data')\n", "tabs.set_title(1, 'PottsCore')\n", "tabs.set_title(2, 'Cell Type')"]}, {"cell_type": "code", "execution_count": null, "id": "bb7a0d2a", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7b89f83d397c452884214404fd373695", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Tab(children=(VBox(children=(HTML(value='<h4>Basic Simulation Setup</h4>'), IntText(value=4, de…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(VBox([tabs, reset_button]))\n", "# Values can't be negative solve by set min=0 \n", "# Default button does not work because of name error \n", "# Cell type format & freeze location is off, the cell type is probably a multi add, not all cell need to be. "]}, {"cell_type": "markdown", "id": "971ea1b6", "metadata": {}, "source": ["# Basic Setup\n", "1. What are the parameters? What can be default and what can be customized? ✅\n", "\n", "## <PERSON><PERSON><PERSON>\n", "Threads & debug output: \n", "1. ? number of CPU/Processor used for simulation ```num_processors```\n", "2. ? frequncy for debugging ```debug_output_frequency```\n", "\n", "Default values:\n", "\n", "```num_processors = 4```\n", "\n", "```debug_output_frequency = 10```\n", "\n", "## PottsCore\n", "All parameters for Cellular Potts Model:\n", "1. Lattice dimensions: ```dim_x, dim_y, dim_z```\n", "2. Number of Monte Carlos Steps: ```steps```\n", "3. <PERSON><PERSON> (Number of annealing steps/initial relaxation): ```anneal```\n", "4. Amplitude of pixel copy fluctuations (temp.): ```fluctuation_amplitude```\n", "5. Fluctuation calculation: ```fluctuation_amplitude_function```\n", "6. Boundary conditions: ```boundary_x, boundary_y, boundary_z```\n", "7. Neighborhood order: ```neighbor_order```\n", "8. Random seed: ```random_seed```\n", "9. <PERSON><PERSON>ce geometry: ```lattice_type```\n", "10. Lattice offset (for hexagonal lattices): ```offset ``` \n", "\n", "Default values:\n", "\n", "```dim_x, dim_y, dim_z = 1,1,1```\n", "\n", "```steps = 0```\n", "\n", "```anneal = 0```\n", "\n", "```fluctuation_amplitude = 10.0```\n", "\n", "```fluctuation_amplitude_function = Min```\n", "\n", "```boundary_x, boundary_y, boundary_z = NoFlux```\n", "\n", "```neighbor_order = 1```\n", "\n", "```random_seed = None```\n", "\n", "```lattice_type = Cartesian```\n", "\n", "```offset = 0``` \n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "05f35335", "metadata": {}, "outputs": [], "source": ["# Potts\n", "from cc3d.core.PyCoreSpecs import Metadata, PottsCore\n"]}, {"cell_type": "markdown", "id": "1026a48a", "metadata": {}, "source": ["# Cell Type\n", "Default ```Medium``` but can set new cell type"]}, {"cell_type": "code", "execution_count": null, "id": "e7cf8304", "metadata": {}, "outputs": [], "source": ["# Cell Type\n", "from cc3d.core.PyCoreSpecs import CellTypePlugin\n"]}, {"cell_type": "markdown", "id": "6d87ade5", "metadata": {}, "source": ["# Initialization\n", "This is like the very last one before the acutal simulation runs, parameters: \n", "- UniformInitializer\n", "- BlobInitializer\n", "- PIFInitializer"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}