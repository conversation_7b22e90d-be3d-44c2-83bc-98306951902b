{"cells": [{"cell_type": "code", "execution_count": null, "id": "36d32632", "metadata": {}, "outputs": [], "source": ["# Imports\n", "import os\n", "import json\n", "import ipywidgets as widgets\n", "from ipywidgets import (\n", "    VBox, HBox, Layout, Dropdown, BoundedIntText, BoundedFloatText,\n", "    FloatText, Checkbox, Button, Text, Label, Tab, HTML\n", ")\n", "from IPython.display import display\n"]}, {"cell_type": "markdown", "id": "430e7c0b", "metadata": {}, "source": ["## Configurations & Default values\n"]}, {"cell_type": "code", "execution_count": null, "id": "0d44553e", "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["# Configuration\n", "SAVE_FILE = 'simulation_setup.json'\n", "\n", "# Default values\n", "DEFAULTS = {\n", "    \"Metadata\": {\n", "        \"num_processors\": 4,\n", "        \"debug_output_frequency\": 10\n", "    },\n", "    \"PottsCore\": {\n", "        \"dim_x\": 100,\n", "        \"dim_y\": 100,\n", "        \"dim_z\": 1,\n", "        \"steps\": 100000,\n", "        \"anneal\": 0,\n", "        \"fluctuation_amplitude\": 10.0,\n", "        \"fluctuation_amplitude_function\": \"Min\",\n", "        \"boundary_x\": \"NoFlux\",\n", "        \"boundary_y\": \"NoFlux\",\n", "        \"boundary_z\": \"NoFlux\",\n", "        \"neighbor_order\": 1,\n", "        \"random_seed\": None,\n", "        \"lattice_type\": \"Cartesian\",\n", "        \"offset\": 0\n", "    },\n", "    \"CellType\": [\n", "        {\"Cell type\": \"Medium\", \"freeze\": False}\n", "    ],\n", "    \"Constraints\": {\n", "        \"Volume\": [\n", "            {\"CellType\": \"Medium\", \"enabled\": False, \"target_volume\": 25.0, \"lambda_volume\": 2.0}\n", "        ],\n", "        \"Surface\": []\n", "    }\n", "}"]}, {"cell_type": "markdown", "id": "0b58433e", "metadata": {}, "source": ["## Helper function\n", "Functions that help save, load, and update parameter values in all UIs."]}, {"cell_type": "code", "execution_count": null, "id": "aba09d2e", "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["# Cell 3: Helper Functions\n", "def load_saved_values():\n", "    \"\"\"Load saved values or use defaults\"\"\"\n", "    try:\n", "        if os.path.exists(SAVE_FILE):\n", "            with open(SAVE_FILE, 'r') as f:\n", "                return json.load(f)\n", "        return DEFAULTS.copy()\n", "    except (j<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>):\n", "        print(\"⚠️ JSON file is corrupted or inaccessible. Resetting to defaults.\")\n", "        return DEFAULTS.copy()\n", "\n", "def current_config(widgets_dict, celltype_entries, constraints):\n", "    \"\"\"Get current configuration from widgets\"\"\"\n", "    return {\n", "        \"Metadata\": {\n", "            \"num_processors\": widgets_dict[\"num_proc\"].value,\n", "            \"debug_output_frequency\": widgets_dict[\"debug_freq\"].value\n", "        },\n", "        \"PottsCore\": {\n", "            \"dim_x\": widgets_dict[\"x_slider\"].value,\n", "            \"dim_y\": widgets_dict[\"y_slider\"].value,\n", "            \"dim_z\": widgets_dict[\"z_slider\"].value,\n", "            \"steps\": widgets_dict[\"steps_input\"].value,\n", "            \"anneal\": widgets_dict[\"anneal_input\"].value,\n", "            \"fluctuation_amplitude\": widgets_dict[\"fluctuation_slider\"].value,\n", "            \"fluctuation_amplitude_function\": widgets_dict[\"flunct_fn_dropdown\"].value,\n", "            \"boundary_x\": widgets_dict[\"boundary_x\"].value,\n", "            \"boundary_y\": widgets_dict[\"boundary_y\"].value,\n", "            \"boundary_z\": widgets_dict[\"boundary_z\"].value,\n", "            \"neighbor_order\": widgets_dict[\"neighbor_order_input\"].value,\n", "            \"random_seed\": int(widgets_dict[\"seed_input\"].value) if widgets_dict[\"seed_input\"].value.strip() else None,\n", "            \"lattice_type\": 'Cartesian' if widgets_dict[\"lattice_dropdown\"].value == 'Square' else 'Hexagonal',\n", "            \"offset\": widgets_dict[\"offset_input\"].value\n", "        },\n", "        \"CellType\": celltype_entries,\n", "        \"Constraints\": constraints\n", "    }\n", "\n", "def save_to_json(config):\n", "    \"\"\"Save current configuration to JSON\"\"\"\n", "    with open(SAVE_FILE, 'w') as f:\n", "        json.dump(config, f, indent=4)\n", "\n", "def update_celltype_display(celltype_entries, display_box):\n", "    \"\"\"Update the cell type display box\"\"\"\n", "    items = []\n", "    for i, entry in enumerate(celltype_entries):\n", "        label_str = entry[\"Cell type\"]\n", "        if entry.get(\"freeze\", False):\n", "            label_str += \" (frozen)\"\n", "        label = Label(label_str, layout=Layout(flex='1 1 auto'))\n", "        remove_btn = Button(description=\"Remove\", button_style='danger', layout=Layout(width='80px'))\n", "        \n", "        def make_remove_handler(index, celltype_entries, display_box):\n", "            def handler(_):\n", "                del celltype_entries[index]\n", "                update_celltype_display(celltype_entries, display_box)\n", "                save_to_json(current_config(widgets_dict, celltype_entries, constraints))\n", "            return handler\n", "        \n", "        remove_btn.on_click(make_remove_handler(i, celltype_entries, display_box))\n", "        items.append(HBox([label, remove_btn], layout=Layout(justify_content='space-between')))\n", "    \n", "    display_box.children = items"]}, {"cell_type": "markdown", "id": "82ea06b3", "metadata": {}, "source": ["## UI\n", "This is where all the Widget are created. \n", "- <PERSON><PERSON><PERSON>\n", "- Potts\n", "- Cell Type\n", "- Volume & Surface Constraint"]}, {"cell_type": "code", "execution_count": null, "id": "ddcb3ca1", "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["# Cell 4: UI Creation Functions\n", "# Initialize state\n", "saved_values = load_saved_values()\n", "celltype_entries = saved_values.get(\"CellType\", DEFAULTS[\"CellType\"].copy())\n", "constraints = saved_values.get(\"Constraints\", DEFAULTS[\"Constraints\"].copy())\n", "\n", "# Create widget dictionaries\n", "widgets_dict = {}\n", "\n", "# Metadata widgets\n", "widgets_dict[\"num_proc\"] = widgets.IntText(\n", "    value=saved_values[\"Metadata\"][\"num_processors\"], min=1,\n", "    description='Number of Processors:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"debug_freq\"] = widgets.IntText(\n", "    value=saved_values[\"Metadata\"][\"debug_output_frequency\"], min=1,\n", "    description='Debug Output Frequency:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# PottsCore widgets\n", "widgets_dict[\"x_slider\"] = widgets.IntSlider(\n", "    value=saved_values[\"PottsCore\"][\"dim_x\"],\n", "    min=0, max=100, description='X:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"y_slider\"] = widgets.IntSlider(\n", "    value=saved_values[\"PottsCore\"][\"dim_y\"],\n", "    min=0, max=100, description='Y:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"z_slider\"] = widgets.IntSlider(\n", "    value=saved_values[\"PottsCore\"][\"dim_z\"],\n", "    min=0, max=100, description='Z:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"steps_input\"] = widgets.IntText(\n", "    value=saved_values[\"PottsCore\"][\"steps\"],\n", "    description='MC Steps:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"anneal_input\"] = widgets.FloatText(\n", "    value=saved_values[\"PottsCore\"][\"anneal\"],\n", "    description='Anneal:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"fluctuation_slider\"] = widgets.FloatSlider(\n", "    value=saved_values[\"PottsCore\"][\"fluctuation_amplitude\"],\n", "    min=0.0, max=50.0, step=0.1,\n", "    description='Fluctuation:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"flunct_fn_dropdown\"] = widgets.Dropdown(\n", "    options=['<PERSON>', '<PERSON>', 'ArithmeticAverage'],\n", "    value=saved_values[\"PottsCore\"][\"fluctuation_amplitude_function\"],\n", "    description='Fluctuation Function:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"boundary_x\"] = widgets.Dropdown(\n", "    options=['NoFlux', 'Periodic'],\n", "    value=saved_values[\"PottsCore\"][\"boundary_x\"],\n", "    description='Boundary X:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"boundary_y\"] = widgets.Dropdown(\n", "    options=['NoFlux', 'Periodic'],\n", "    value=saved_values[\"PottsCore\"][\"boundary_y\"],\n", "    description='Boundary Y:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"boundary_z\"] = widgets.Dropdown(\n", "    options=['NoFlux', 'Periodic'],\n", "    value=saved_values[\"PottsCore\"][\"boundary_z\"],\n", "    description='Boundary Z:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"neighbor_order_input\"] = widgets.BoundedIntText(\n", "    value=saved_values[\"PottsCore\"][\"neighbor_order\"],\n", "    min=1, max=20, description='Neighbor Order:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"seed_input\"] = widgets.Text(\n", "    value='' if saved_values[\"PottsCore\"][\"random_seed\"] is None \n", "         else str(saved_values[\"PottsCore\"][\"random_seed\"]),\n", "    description='Random Seed:',\n", "    placeholder='e.g. 123456',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"lattice_dropdown\"] = widgets.Dropdown(\n", "    options=['Square', 'Hexagonal'],\n", "    value='Square' if saved_values[\"PottsCore\"][\"lattice_type\"] == 'Cartesian' \n", "          else 'Hexagonal',\n", "    description='Lattice Type:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"offset_input\"] = widgets.IntText(\n", "    value=saved_values[\"PottsCore\"][\"offset\"],\n", "    description='Offset:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# CellType widgets\n", "widgets_dict[\"celltype_display_box\"] = VBox(\n", "    layout=Layout(border='1px solid gray', padding='10px', width='300px')\n", ")\n", "update_celltype_display(celltype_entries, widgets_dict[\"celltype_display_box\"])\n", "\n", "widgets_dict[\"preset_dropdown\"] = Dropdown(\n", "    options=[\"Medium\", \"Condensing\", \"NonCondensing\", \"Customize\"],\n", "    value=\"Medium\",\n", "    description=\"Cell Type:\",\n", "    style={'description_width': 'initial'},\n", "    layout=Layout(width='250px')\n", ")\n", "widgets_dict[\"freeze_checkbox\"] = Checkbox(\n", "    value=True,\n", "    description=\"Freeze\",\n", "    indent=False,\n", "    layout=Layout(margin='0 0 0 20px')\n", ")\n", "widgets_dict[\"custom_name_input\"] = Text(\n", "    description=\"Name:\",\n", "    placeholder=\"e.g. T1\",\n", "    style={'description_width': 'initial'},\n", "    layout=Layout(width='250px')\n", ")\n", "widgets_dict[\"custom_name_input\"].layout.display = 'none'\n", "widgets_dict[\"add_button\"] = Button(\n", "    description=\"Add\",\n", "    button_style=\"success\",\n", "    layout=Layout(width='80px', margin='10px 0 0 0')\n", ")\n", "\n", "# Constraints widgets\n", "widgets_dict[\"constraints_display_box\"] = VBox(\n", "    layout=Layout(border='1px solid gray', padding='10px', width='500px'))\n", "widgets_dict[\"constraints_celltype_dropdown\"] = widgets.Dropdown(\n", "    options=[entry[\"Cell type\"] for entry in celltype_entries],\n", "    description='Cell Type:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"vol_enabled\"] = widgets.Checkbox(\n", "    value=False,\n", "    description=\"Enable Volume Constraints\",\n", "    indent=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"target_volume\"] = widgets.FloatText(\n", "    value=25.0,\n", "    min=1.0,\n", "    description='Target Volume:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"lambda_volume\"] = widgets.FloatText(\n", "    value=2.0,\n", "    min=0.0,\n", "    description='Lambda Volume:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"surf_enabled\"] = widgets.Checkbox(\n", "    value=False,\n", "    description=\"Enable Surface Constraints\",\n", "    indent=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"target_surface\"] = widgets.FloatText(\n", "    value=100.0,\n", "    min=1.0,\n", "    description='Target Surface:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"lambda_surface\"] = widgets.FloatText(\n", "    value=0.5,\n", "    min=0.0,\n", "    description='Lambda Surface:',\n", "    style={'description_width': 'initial'}\n", ")\n", "widgets_dict[\"add_constraints_button\"] = Button(\n", "    description=\"Add Constraints\",\n", "    button_style=\"success\",\n", "    layout=Layout(width='150px')\n", ")\n", "\n", "# Reset buttons\n", "widgets_dict[\"reset_metadata_button\"] = Button(\n", "    description=\"Reset Tab\",\n", "    button_style='warning',\n", "    layout=Layout(width='100px')\n", ")\n", "widgets_dict[\"reset_potts_button\"] = Button(\n", "    description=\"Reset Tab\",\n", "    button_style='warning',\n", "    layout=Layout(width='100px')\n", ")\n", "widgets_dict[\"reset_ct_button\"] = Button(\n", "    description=\"Reset Tab\",\n", "    button_style='warning',\n", "    layout=Layout(width='100px')\n", ")\n", "widgets_dict[\"reset_constraints_button\"] = Button(\n", "    description=\"Reset Tab\",\n", "    button_style='warning',\n", "    layout=Layout(width='100px')\n", ")\n", "widgets_dict[\"reset_button\"] = Button(\n", "    description=\"Reset All to Defaults\",\n", "    button_style='danger'\n", ")"]}, {"cell_type": "markdown", "id": "92f20173", "metadata": {}, "source": ["## Layout\n", "Mainly how each wigets are formatted in the tabs"]}, {"cell_type": "code", "execution_count": null, "id": "83ca9a1d", "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["# Cell 5: UI Layout Functions\n", "def create_metadata_tab():\n", "    return VBox([\n", "        widgets_dict[\"num_proc\"],\n", "        widgets_dict[\"debug_freq\"],\n", "        widgets_dict[\"reset_metadata_button\"]\n", "    ], layout=Layout(padding='10px'))\n", "\n", "def create_potts_tab():\n", "    return VBox([\n", "        HTML(\"<b>Dimensions:</b>\"),\n", "        HBox([widgets_dict[\"x_slider\"], widgets_dict[\"y_slider\"], widgets_dict[\"z_slider\"]]),\n", "        HTML(\"<b>Core Parameters:</b>\"),\n", "        widgets_dict[\"steps_input\"],\n", "        widgets_dict[\"anneal_input\"],\n", "        widgets_dict[\"fluctuation_slider\"],\n", "        widgets_dict[\"flunct_fn_dropdown\"],\n", "        HTML(\"<b>Boundaries:</b>\"),\n", "        HBox([widgets_dict[\"boundary_x\"], widgets_dict[\"boundary_y\"], widgets_dict[\"boundary_z\"]]),\n", "        HTML(\"<b>Advanced:</b>\"),\n", "        widgets_dict[\"neighbor_order_input\"],\n", "        widgets_dict[\"lattice_dropdown\"],\n", "        widgets_dict[\"offset_input\"],\n", "        widgets_dict[\"seed_input\"],\n", "        widgets_dict[\"reset_potts_button\"]\n", "    ], layout=Layout(padding='10px'))\n", "\n", "def create_celltype_tab():\n", "    cell_type_row = HBox([\n", "        widgets_dict[\"preset_dropdown\"],\n", "        widgets_dict[\"freeze_checkbox\"]\n", "    ])\n", "    \n", "    return VBox([\n", "        HBox([\n", "            VBox([\n", "                Label(\"Current Cell Types:\", style={'font_weight': 'bold'}),\n", "                widgets_dict[\"celltype_display_box\"],\n", "                widgets_dict[\"reset_ct_button\"]\n", "            ], layout=Layout(width='320px', padding='0 20px 0 0')),\n", "            VBox([\n", "                Label(\"Add Cell Type:\", style={'font_weight': 'bold'}),\n", "                cell_type_row,\n", "                widgets_dict[\"custom_name_input\"],\n", "                widgets_dict[\"add_button\"]\n", "            ])\n", "        ])\n", "    ], layout=Layout(padding='10px'))\n", "\n", "def create_constraints_tab():\n", "    return VBox([\n", "        HBox([\n", "            widgets_dict[\"constraints_celltype_dropdown\"],\n", "            widgets_dict[\"add_constraints_button\"]\n", "        ]),\n", "        HTML(\"<hr><b>Volume Constraints:</b>\"),\n", "        widgets_dict[\"vol_enabled\"],\n", "        HBox([widgets_dict[\"target_volume\"], widgets_dict[\"lambda_volume\"]]),\n", "        HTML(\"<hr><b>Surface Constraints:</b>\"),\n", "        widgets_dict[\"surf_enabled\"],\n", "        HBox([widgets_dict[\"target_surface\"], widgets_dict[\"lambda_surface\"]]),\n", "        HTML(\"<hr><b>Current Constraints:</b>\"),\n", "        widgets_dict[\"constraints_display_box\"],\n", "        widgets_dict[\"reset_constraints_button\"]\n", "    ], layout=Layout(padding='10px'))\n", "\n", "def setup_tabs():\n", "    tabs = Tab()\n", "    tabs.children = [\n", "        create_metadata_tab(),\n", "        create_potts_tab(),\n", "        create_celltype_tab(),\n", "        create_constraints_tab()\n", "    ]\n", "    tabs.set_title(0, 'Basic Setup')\n", "    tabs.set_title(1, 'Potts Core')\n", "    tabs.set_title(2, 'Cell Types')\n", "    tabs.set_title(3, 'Constraints')\n", "    return tabs"]}, {"cell_type": "markdown", "id": "a77b2e86", "metadata": {}, "source": ["## Response to user \n", "Functions are defined to update information when items are add/removed or reset. Mostly functional buttons"]}, {"cell_type": "code", "execution_count": null, "id": "da97adb1", "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["# Cell 6: Event Handlers\n", "def toggle_custom_input(change):\n", "    widgets_dict[\"custom_name_input\"].layout.display = 'block' if change['new'] == \"Customize\" else 'none'\n", "\n", "def on_add_clicked(_):\n", "    selected = widgets_dict[\"preset_dropdown\"].value\n", "    name = (widgets_dict[\"custom_name_input\"].value.strip() \n", "            if selected == \"Customize\" else selected)\n", "    \n", "    if not name:\n", "        widgets_dict[\"custom_name_input\"].placeholder = \"Please enter a name!\"\n", "        return\n", "    \n", "    celltype_entries.append({\n", "        \"Cell type\": name, \n", "        \"freeze\": widgets_dict[\"freeze_checkbox\"].value\n", "    })\n", "    \n", "    update_celltype_display(celltype_entries, widgets_dict[\"celltype_display_box\"])\n", "    widgets_dict[\"constraints_celltype_dropdown\"].options = [entry[\"Cell type\"] for entry in celltype_entries]\n", "    widgets_dict[\"custom_name_input\"].value = \"\"\n", "    save_to_json(current_config(widgets_dict, celltype_entries, constraints))\n", "\n", "def update_constraints_display():\n", "    \"\"\"Update the constraints display box\"\"\"\n", "    items = []\n", "    \n", "    # Volume constraints\n", "    vol_header = Label(\"Volume Constraints:\", style={'font_weight': 'bold', 'font_style': 'italic'})\n", "    items.append(vol_header)\n", "    \n", "    for entry in constraints[\"Volume\"]:\n", "        ct = entry[\"CellType\"]\n", "        enabled = \"✓\" if entry[\"enabled\"] else \"✗\"\n", "        text = f\"{ct}: Enabled={enabled}, Target={entry['target_volume']}, λ={entry['lambda_volume']:.2f}\"\n", "        label = Label(text, layout=Layout(flex='1 1 auto'))\n", "        \n", "        remove_btn = Button(description=\"Remove\", button_style='danger', layout=Layout(width='80px'))\n", "        \n", "        def make_remove_handler(ct):\n", "            def handler(_):\n", "                constraints[\"Volume\"] = [e for e in constraints[\"Volume\"] if e[\"CellType\"] != ct]\n", "                constraints[\"Surface\"] = [e for e in constraints[\"Surface\"] if e[\"CellType\"] != ct]\n", "                save_to_json(current_config(widgets_dict, celltype_entries, constraints))\n", "                update_constraints_display()\n", "            return handler\n", "        \n", "        remove_btn.on_click(make_remove_handler(ct))\n", "        items.append(HBox([label, remove_btn], layout=Layout(justify_content='space-between')))\n", "    \n", "    # Surface constraints\n", "    surf_header = Label(\"Surface Constraints:\", style={'font_weight': 'bold', 'font_style': 'italic'})\n", "    items.append(surf_header)\n", "    \n", "    for entry in constraints[\"Surface\"]:\n", "        ct = entry[\"CellType\"]\n", "        enabled = \"✓\" if entry[\"enabled\"] else \"✗\"\n", "        text = f\"{ct}: Enabled={enabled}, Target={entry['target_surface']}, λ={entry['lambda_surface']:.2f}\"\n", "        label = Label(text, layout=Layout(flex='1 1 auto'))\n", "        \n", "        remove_btn = Button(description=\"Remove\", button_style='danger', layout=Layout(width='80px'))\n", "        \n", "        def make_remove_handler(ct):\n", "            def handler(_):\n", "                constraints[\"Surface\"] = [e for e in constraints[\"Surface\"] if e[\"CellType\"] != ct]\n", "                save_to_json(current_config(widgets_dict, celltype_entries, constraints))\n", "                update_constraints_display()\n", "            return handler\n", "        \n", "        remove_btn.on_click(make_remove_handler(ct))\n", "        items.append(HBox([label, remove_btn], layout=Layout(justify_content='space-between')))\n", "    \n", "    if not constraints[\"Volume\"] and not constraints[\"Surface\"]:\n", "        items.append(Label(\"No constraints defined\"))\n", "    \n", "    widgets_dict[\"constraints_display_box\"].children = items\n", "\n", "def on_add_constraints_clicked(_):\n", "    cell_type = widgets_dict[\"constraints_celltype_dropdown\"].value\n", "    \n", "    # Add/update volume constraints\n", "    vol_exists = False\n", "    for entry in constraints[\"Volume\"]:\n", "        if entry[\"CellType\"] == cell_type:\n", "            entry.update({\n", "                \"enabled\": widgets_dict[\"vol_enabled\"].value,\n", "                \"target_volume\": widgets_dict[\"target_volume\"].value,\n", "                \"lambda_volume\": widgets_dict[\"lambda_volume\"].value\n", "            })\n", "            vol_exists = True\n", "            break\n", "    \n", "    if not vol_exists:\n", "        constraints[\"Volume\"].append({\n", "            \"CellType\": cell_type,\n", "            \"enabled\": widgets_dict[\"vol_enabled\"].value,\n", "            \"target_volume\": widgets_dict[\"target_volume\"].value,\n", "            \"lambda_volume\": widgets_dict[\"lambda_volume\"].value\n", "        })\n", "    \n", "    # Add/update surface constraints\n", "    surf_exists = False\n", "    for entry in constraints[\"Surface\"]:\n", "        if entry[\"CellType\"] == cell_type:\n", "            entry.update({\n", "                \"enabled\": widgets_dict[\"surf_enabled\"].value,\n", "                \"target_surface\": widgets_dict[\"target_surface\"].value,\n", "                \"lambda_surface\": widgets_dict[\"lambda_surface\"].value\n", "            })\n", "            surf_exists = True\n", "            break\n", "    \n", "    if not surf_exists and widgets_dict[\"surf_enabled\"].value:\n", "        constraints[\"Surface\"].append({\n", "            \"CellType\": cell_type,\n", "            \"enabled\": widgets_dict[\"surf_enabled\"].value,\n", "            \"target_surface\": widgets_dict[\"target_surface\"].value,\n", "            \"lambda_surface\": widgets_dict[\"lambda_surface\"].value\n", "        })\n", "    \n", "    save_to_json(current_config(widgets_dict, celltype_entries, constraints))\n", "    update_constraints_display()\n", "\n", "def reset_tab(tab_name):\n", "    if tab_name == \"Metadata\":\n", "        widgets_dict[\"num_proc\"].value = DEFAULTS[\"Metadata\"][\"num_processors\"]\n", "        widgets_dict[\"debug_freq\"].value = DEFAULTS[\"Metadata\"][\"debug_output_frequency\"]\n", "    \n", "    elif tab_name == \"PottsCore\":\n", "        widgets_dict[\"x_slider\"].value = DEFAULTS[\"PottsCore\"][\"dim_x\"]\n", "        widgets_dict[\"y_slider\"].value = DEFAULTS[\"PottsCore\"][\"dim_y\"]\n", "        widgets_dict[\"z_slider\"].value = DEFAULTS[\"PottsCore\"][\"dim_z\"]\n", "        widgets_dict[\"steps_input\"].value = DEFAULTS[\"PottsCore\"][\"steps\"]\n", "        widgets_dict[\"anneal_input\"].value = DEFAULTS[\"PottsCore\"][\"anneal\"]\n", "        widgets_dict[\"fluctuation_slider\"].value = DEFAULTS[\"PottsCore\"][\"fluctuation_amplitude\"]\n", "        widgets_dict[\"flunct_fn_dropdown\"].value = DEFAULTS[\"PottsCore\"][\"fluctuation_amplitude_function\"]\n", "        widgets_dict[\"boundary_x\"].value = DEFAULTS[\"PottsCore\"][\"boundary_x\"]\n", "        widgets_dict[\"boundary_y\"].value = DEFAULTS[\"PottsCore\"][\"boundary_y\"]\n", "        widgets_dict[\"boundary_z\"].value = DEFAULTS[\"PottsCore\"][\"boundary_z\"]\n", "        widgets_dict[\"neighbor_order_input\"].value = DEFAULTS[\"PottsCore\"][\"neighbor_order\"]\n", "        widgets_dict[\"seed_input\"].value = ''\n", "        widgets_dict[\"lattice_dropdown\"].value = 'Square' if DEFAULTS[\"PottsCore\"][\"lattice_type\"] == 'Cartesian' else 'Hexagonal'\n", "        widgets_dict[\"offset_input\"].value = DEFAULTS[\"PottsCore\"][\"offset\"]\n", "    \n", "    elif tab_name == \"CellType\":\n", "        global celltype_entries\n", "        celltype_entries = DEFAULTS[\"CellType\"].copy()\n", "        update_celltype_display(celltype_entries, widgets_dict[\"celltype_display_box\"])\n", "        widgets_dict[\"constraints_celltype_dropdown\"].options = [entry[\"Cell type\"] for entry in celltype_entries]\n", "    \n", "    elif tab_name == \"Constraints\":\n", "        global constraints\n", "        constraints = DEFAULTS[\"Constraints\"].copy()\n", "        update_constraints_display()\n", "    \n", "    save_to_json(current_config(widgets_dict, celltype_entries, constraints))\n", "\n", "def reset_to_defaults(_):\n", "    reset_tab(\"Metadata\")\n", "    reset_tab(\"PottsCore\")\n", "    reset_tab(\"CellType\")\n", "    reset_tab(\"Constraints\")\n", "\n", "# Set up event handlers\n", "widgets_dict[\"preset_dropdown\"].observe(toggle_custom_input, names='value')\n", "widgets_dict[\"add_button\"].on_click(on_add_clicked)\n", "widgets_dict[\"add_constraints_button\"].on_click(on_add_constraints_clicked)\n", "\n", "widgets_dict[\"reset_metadata_button\"].on_click(lambda _: reset_tab(\"Metadata\"))\n", "widgets_dict[\"reset_potts_button\"].on_click(lambda _: reset_tab(\"PottsCore\"))\n", "widgets_dict[\"reset_ct_button\"].on_click(lambda _: reset_tab(\"CellType\"))\n", "widgets_dict[\"reset_constraints_button\"].on_click(lambda _: reset_tab(\"Constraints\"))\n", "widgets_dict[\"reset_button\"].on_click(reset_to_defaults)\n", "\n", "# Set save triggers\n", "for w in [widgets_dict[k] for k in [\n", "    \"num_proc\", \"debug_freq\", \"x_slider\", \"y_slider\", \"z_slider\",\n", "    \"steps_input\", \"anneal_input\", \"fluctuation_slider\", \"flunct_fn_dropdown\",\n", "    \"neighbor_order_input\", \"boundary_x\", \"boundary_y\", \"boundary_z\",\n", "    \"lattice_dropdown\", \"offset_input\", \"seed_input\"\n", "]]:\n", "    w.observe(lambda _: save_to_json(current_config(widgets_dict, celltype_entries, constraints)), names='value')\n", "\n", "# Initialize constraints display\n", "update_constraints_display()"]}, {"cell_type": "code", "execution_count": null, "id": "fdabfe2a", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b11a1f30f90e4f2f9134041ef1996a07", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Tab(children=(VBox(children=(IntText(value=4, description='Number of Processors:', style=Descri…"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["# Cell 7: Main UI Assembly\n", "# Create tabs\n", "tabs = setup_tabs()\n", "\n", "# Display everything\n", "display(VBox([\n", "    tabs,\n", "    widgets_dict[\"reset_button\"]\n", "]))"]}, {"cell_type": "markdown", "id": "6c1e94dd", "metadata": {}, "source": ["# Change to class, cell type and Potts seperate class class CellTypeWidgetO PottsWidget()\n", "# Connect to compucell --> spec list (register the sepc list, and runnning the simulation)"]}, {"cell_type": "markdown", "id": "ed7cd454", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}