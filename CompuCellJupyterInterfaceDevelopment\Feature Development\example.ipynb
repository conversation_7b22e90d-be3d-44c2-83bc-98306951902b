{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6581d5e7", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "<style>\n", "/* Round corners for all input boxes */\n", ".widget-text input,\n", ".widget-bounded-int-text input,\n", ".widget-bounded-float-text input,\n", ".widget-float-text input,\n", ".widget-int-text input {\n", "    border-radius: 4px !important;\n", "}\n", "\n", "/* Round corners for dropdown/select inputs */\n", ".widget-dropdown select,\n", ".widget-select select {\n", "    border-radius: 4px !important;\n", "}\n", "\n", "/* Round corners for buttons */\n", ".widget-button button,\n", ".jupyter-button {\n", "    border-radius: 4px !important;\n", "}\n", "\n", "/* Spacing classes for layout containers */\n", ".vbox-row-spacing {\n", "    margin: 10px 0 !important;\n", "}\n", "\n", ".hbox-item-spacing {\n", "    margin: 0 15px 0 0 !important;\n", "}\n", "\n", ".vbox-no-margin {\n", "    margin: 0 !important;\n", "}\n", "\n", ".hbox-no-margin {\n", "    margin: 0 !important;\n", "}\n", "\n", ".celltype-item-spacing {\n", "    margin: 0 0 5px 0 !important;\n", "}\n", "\n", ".small-right-spacing {\n", "    margin: 0 5px 0 0 !important;\n", "}\n", "\n", "/* Plugin-specific spacing classes */\n", ".plugin-config-container {\n", "    padding: 5px 0 0 20px !important;\n", "}\n", "\n", ".plugin-input-spacing {\n", "    margin: 0 15px 0 0 !important;\n", "}\n", "\n", ".plugin-bottom-spacing {\n", "    margin: 0 0 15px 0 !important;\n", "}\n", "\n", ".button-spacing {\n", "    margin: 10px 0 !important;\n", "}\n", "\n", "/* Error state styling with rounded corners */\n", ".error-input input {\n", "    border: 2px solid #f44336 !important;\n", "    background-color: #ffebee !important;\n", "    box-shadow: 0 0 3px rgba(244, 67, 54, 0.3) !important;\n", "    border-radius: 4px !important;\n", "}\n", "\n", ".error-input input:focus {\n", "    border-color: #d32f2f !important;\n", "    box-shadow: 0 0 5px rgba(244, 67, 54, 0.5) !important;\n", "    border-radius: 4px !important;\n", "}\n", "</style>\n"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Added cell type: Medium, freeze: False\n", "Total cell types: 1\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "55fc6fb619b14be3a0900e4ae616f5d5", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Tab(children=(VBox(children=(HTML(value='<b>Simulation Metadata</b>'), VBox(children=(IntText(v…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\" \\n- Redundency if both front/back check;\\n- Rely more on the defition of constraint + display that text to user \\n- Frontend adjustment as user can't select negative value --> min values are usually defined in the API but not max values,\\nmax values might need to rely on defintion check. \\n\""]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}, {"ename": "SpecValueCheckError", "evalue": "Dimension must be positive", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mSpecV<PERSON>ueCheckError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "File \u001b[1;32ma:\\conda_envs\\cc3d_env\\lib\\site-packages\\ipywidgets\\widgets\\widget.py:773\u001b[0m, in \u001b[0;36mWidget._handle_msg\u001b[1;34m(self, msg)\u001b[0m\n\u001b[0;32m    771\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbuffer_paths\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01min\u001b[39;00m data:\n\u001b[0;32m    772\u001b[0m             _put_buffers(state, data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbuffer_paths\u001b[39m\u001b[38;5;124m'\u001b[39m], msg[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbuffers\u001b[39m\u001b[38;5;124m'\u001b[39m])\n\u001b[1;32m--> 773\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mset_state\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstate\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    775\u001b[0m \u001b[38;5;66;03m# Handle a state request.\u001b[39;00m\n\u001b[0;32m    776\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m method \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mrequest_state\u001b[39m\u001b[38;5;124m'\u001b[39m:\n", "File \u001b[1;32ma:\\conda_envs\\cc3d_env\\lib\\site-packages\\ipywidgets\\widgets\\widget.py:650\u001b[0m, in \u001b[0;36mWidget.set_state\u001b[1;34m(self, sync_data)\u001b[0m\n\u001b[0;32m    645\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send(msg, buffers\u001b[38;5;241m=\u001b[39mecho_buffers)\n\u001b[0;32m    647\u001b[0m \u001b[38;5;66;03m# The order of these context managers is important. Properties must\u001b[39;00m\n\u001b[0;32m    648\u001b[0m \u001b[38;5;66;03m# be locked when the hold_trait_notification context manager is\u001b[39;00m\n\u001b[0;32m    649\u001b[0m \u001b[38;5;66;03m# released and notifications are fired.\u001b[39;00m\n\u001b[1;32m--> 650\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_lock_property(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39msync_data), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhold_trait_notifications():\n\u001b[0;32m    651\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m sync_data:\n\u001b[0;32m    652\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mkeys:\n", "File \u001b[1;32ma:\\conda_envs\\cc3d_env\\lib\\contextlib.py:142\u001b[0m, in \u001b[0;36m_GeneratorContextManager.__exit__\u001b[1;34m(self, typ, value, traceback)\u001b[0m\n\u001b[0;32m    140\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m typ \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m    141\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 142\u001b[0m         \u001b[38;5;28;43mnext\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgen\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    143\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m:\n\u001b[0;32m    144\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>e\u001b[39;00m\n", "File \u001b[1;32ma:\\conda_envs\\cc3d_env\\lib\\site-packages\\traitlets\\traitlets.py:1510\u001b[0m, in \u001b[0;36mHasTraits.hold_trait_notifications\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1508\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m changes \u001b[38;5;129;01min\u001b[39;00m cache\u001b[38;5;241m.\u001b[39mvalues():\n\u001b[0;32m   1509\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m change \u001b[38;5;129;01min\u001b[39;00m changes:\n\u001b[1;32m-> 1510\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnotify_change\u001b[49m\u001b[43m(\u001b[49m\u001b[43mchange\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32ma:\\conda_envs\\cc3d_env\\lib\\site-packages\\ipywidgets\\widgets\\widget.py:701\u001b[0m, in \u001b[0;36mWidget.notify_change\u001b[1;34m(self, change)\u001b[0m\n\u001b[0;32m    698\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mkeys \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_should_send_property(name, \u001b[38;5;28mgetattr\u001b[39m(\u001b[38;5;28mself\u001b[39m, name)):\n\u001b[0;32m    699\u001b[0m         \u001b[38;5;66;03m# Send new state to front-end\u001b[39;00m\n\u001b[0;32m    700\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msend_state(key\u001b[38;5;241m=\u001b[39mname)\n\u001b[1;32m--> 701\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnotify_change\u001b[49m\u001b[43m(\u001b[49m\u001b[43mchange\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32ma:\\conda_envs\\cc3d_env\\lib\\site-packages\\traitlets\\traitlets.py:1525\u001b[0m, in \u001b[0;36mHasTraits.notify_change\u001b[1;34m(self, change)\u001b[0m\n\u001b[0;32m   1523\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mnotify_change\u001b[39m(\u001b[38;5;28mself\u001b[39m, change: Bunch) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m   1524\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Notify observers of a change event\"\"\"\u001b[39;00m\n\u001b[1;32m-> 1525\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_notify_observers\u001b[49m\u001b[43m(\u001b[49m\u001b[43mchange\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32ma:\\conda_envs\\cc3d_env\\lib\\site-packages\\traitlets\\traitlets.py:1568\u001b[0m, in \u001b[0;36mHasTraits._notify_observers\u001b[1;34m(self, event)\u001b[0m\n\u001b[0;32m   1565\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(c, <PERSON><PERSON><PERSON><PERSON>) \u001b[38;5;129;01mand\u001b[39;00m c\u001b[38;5;241m.\u001b[39mname \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m   1566\u001b[0m     c \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(\u001b[38;5;28mself\u001b[39m, c\u001b[38;5;241m.\u001b[39mname)\n\u001b[1;32m-> 1568\u001b[0m \u001b[43mc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mevent\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mb:\\Projects\\AT3\\Keke_CompuCell3D_fork\\CompuCellJupyterInterfaceDevelopment\\Feature Development\\Spec_UI.py:1722\u001b[0m, in \u001b[0;36mSpecificationSetupUI.setup_event_handlers.<locals>.<lambda>\u001b[1;34m(change, prop)\u001b[0m\n\u001b[0;32m   1719\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m name, widget \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpotts_widget\u001b[38;5;241m.\u001b[39mwidgets\u001b[38;5;241m.\u001b[39mitems():\n\u001b[0;32m   1720\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(widget, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mobserve\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;129;01mand\u001b[39;00m name \u001b[38;5;241m!=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mreset_button\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m   1721\u001b[0m         widget\u001b[38;5;241m.\u001b[39mobserve(\n\u001b[1;32m-> 1722\u001b[0m             \u001b[38;5;28;01mlambda\u001b[39;00m change, prop\u001b[38;5;241m=\u001b[39mname: \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mupdate_potts_core\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mchange\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnew\u001b[49m\u001b[43m)\u001b[49m,\n\u001b[0;32m   1723\u001b[0m             names\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m   1724\u001b[0m         )\n\u001b[0;32m   1726\u001b[0m \u001b[38;5;66;03m# CellType handlers\u001b[39;00m\n\u001b[0;32m   1727\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcelltype_widget\u001b[38;5;241m.\u001b[39mwidgets[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124madd_button\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mon_click(\n\u001b[0;32m   1728\u001b[0m     \u001b[38;5;28;01mlambda\u001b[39;00m _: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mupdate_cell_types()\n\u001b[0;32m   1729\u001b[0m )\n", "File \u001b[1;32mb:\\Projects\\AT3\\Keke_CompuCell3D_fork\\CompuCellJupyterInterfaceDevelopment\\Feature Development\\Spec_UI.py:1741\u001b[0m, in \u001b[0;36mSpecificationSetupUI.update_potts_core\u001b[1;34m(self, property_name, value)\u001b[0m\n\u001b[0;32m   1739\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mupdate_potts_core\u001b[39m(\u001b[38;5;28mself\u001b[39m, property_name, value):\n\u001b[0;32m   1740\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpotts_core, property_name):\n\u001b[1;32m-> 1741\u001b[0m         \u001b[38;5;28;43msetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpotts_core\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproperty_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1742\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msave_to_json()\n", "File \u001b[1;32ma:\\conda_envs\\cc3d_env\\lib\\site-packages\\cc3d\\core\\PyCoreSpecs.py:175\u001b[0m, in \u001b[0;36mSpecProperty.__init__.<locals>._fset\u001b[1;34m(_self, val)\u001b[0m\n\u001b[0;32m    173\u001b[0m     fcn, msg \u001b[38;5;241m=\u001b[39m _self\u001b[38;5;241m.\u001b[39mcheck_dict[name]\n\u001b[0;32m    174\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m fcn(val):\n\u001b[1;32m--> 175\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m SpecValueCheckError(msg, names\u001b[38;5;241m=\u001b[39m[name])\n\u001b[0;32m    176\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m:\n\u001b[0;32m    177\u001b[0m     \u001b[38;5;28;01mpass\u001b[39;00m\n", "\u001b[1;31mSpecValueCheckError\u001b[0m: Dimension must be positive"]}], "source": ["from Spec_UI import SpecificationSetupUI\n", "\n", "ui = SpecificationSetupUI()\n", "\n", "# Known Issue:\n", "# 1. Cell Type Id assignment issue (no need manual assign)\n", "# 2. Save configuration supose to validate/save to json (right now it's just UI)\n", "''' \n", "The \"Save Configuration\" button might not be needed as .spec_dict validate constraint. \n", "\n", "'''\n", "# 3. Plugins grouping are not fully correct | Plugins are not saved \n", "'''\n", "Follow the Widzard simulation? or how should this be grouped?\n", "Data are not stored in the json file as they suppose to --> debug file. \n", "(<PERSON><PERSON><PERSON> HTML goal) Also, the UI can be fixed vertical tabs, tabs in a tabs seems a little weird \n", "'''\n", "\n", "# 4. Range constraint rely on defintion and widget min/max adjustment  \n", "''' \n", "- Redundency if both front/back check;\n", "- Rely more on the defition of constraint + display that text to user \n", "- Frontend adjustment as user can't select negative value --> min values are usually defined in the API but not max values,\n", "max values might need to rely on defintion check. \n", "'''\n"]}, {"cell_type": "code", "execution_count": null, "id": "07189377", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Verifying Cell ID Assignment...\n", "\n", "1️⃣ Testing CompuCell3D ID Assignment:\n", "=== Testing Cell Type ID Assignment ===\n", "Cell types in CompuCell3D:\n"]}, {"ename": "AttributeError", "evalue": "'str' object has no attribute 'type_name'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 6\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;66;03m# Method 1: Test CompuCell3D ID assignment directly\u001b[39;00m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m1️⃣ Testing CompuCell3D ID Assignment:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m----> 6\u001b[0m cell_types_with_ids \u001b[38;5;241m=\u001b[39m \u001b[43mui\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtest_cell_type_ids\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      8\u001b[0m \u001b[38;5;66;03m# Method 2: Check what's in your current UI\u001b[39;00m\n\u001b[0;32m      9\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m2️⃣ Current Cell Types in UI:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32mb:\\Projects\\AT3\\Keke_CompuCell3D_fork\\CompuCellJupyterInterfaceDevelopment\\Feature Development\\Spec_UI.py:1650\u001b[0m, in \u001b[0;36mSpecificationSetupUI.test_cell_type_ids\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1648\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCell types in CompuCell3D:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   1649\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i, cell_type \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcell_type_plugin\u001b[38;5;241m.\u001b[39mcell_types):\n\u001b[1;32m-> 1650\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mID \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mi\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[43mcell_type\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtype_name\u001b[49m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m (frozen: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mcell_type\u001b[38;5;241m.\u001b[39mfrozen\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m)\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   1652\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m=== Test Complete ===\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   1653\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcell_type_plugin\u001b[38;5;241m.\u001b[39mcell_types\n", "\u001b[1;31mAttributeError\u001b[0m: 'str' object has no attribute 'type_name'"]}], "source": ["# Simple Cell ID Test (without using the problematic method)\n", "print(\"🔍 Simple Cell ID Verification...\")\n", "\n", "# Check current UI state\n", "print(\"\\n📋 Current Cell Types in UI:\")\n", "ui.celltype_widget.debug_cell_types()\n", "\n", "# Check the plugin directly\n", "print(\"\\n🔧 Direct Plugin Inspection:\")\n", "if hasattr(ui, 'cell_type_plugin') and ui.cell_type_plugin:\n", "    print(f\"Plugin type: {type(ui.cell_type_plugin)}\")\n", "    print(f\"Cell types: {ui.cell_type_plugin.cell_types}\")\n", "    print(f\"Cell types type: {type(ui.cell_type_plugin.cell_types)}\")\n", "    \n", "    # Safe iteration\n", "    for i, ct in enumerate(ui.cell_type_plugin.cell_types):\n", "        print(f\"  Index {i}: '{ct}' (type: {type(ct)})\")\n", "        \n", "    # Check spec_dict\n", "    if hasattr(ui.cell_type_plugin, 'spec_dict'):\n", "        print(f\"Spec dict: {ui.cell_type_plugin.spec_dict}\")\n", "else:\n", "    print(\"No cell type plugin found\")\n", "\n", "# Check saved configuration\n", "print(\"\\n💾 Configuration Check:\")\n", "config = ui.current_config()\n", "print(f\"Cell types in config: {config.get('CellType', [])}\")\n", "\n", "print(\"\\n✅ Simple test complete!\")"]}, {"cell_type": "code", "execution_count": 2, "id": "6da8bb15", "metadata": {}, "outputs": [], "source": ["# %run ./Spec_UI.py IGNORE"]}, {"cell_type": "code", "execution_count": 3, "id": "21f0784e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Plugin: AdhesionFlexPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "07989e03821a4c0e846406594a04dcda", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='1', description='neighbor_order')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "452bca09a484438d90474c22752dc0f4", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='[]', description='molecules')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "99c7e728e72d409593aa001dbbfe9ad6", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='densities')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a3986b44ef29442faadc4a05ad4dd8a2", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='binding_formulas')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: BoundaryPixelTrackerPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6fe39c90c5664c9592c321ed39151bc3", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='1', description='neighbor_order')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: CellTypePlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "55135a617298466a8419a13617072dc5", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value=\"[('Medium', 0, False)]\", description='cell_types')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: CenterOfMassPlugin\n", "Plugin: ChemotaxisPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ceda8b8bc1b64b7c98afaa93f07f2ae2", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='field_specs')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: ConnectivityGlobalPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c0099a58f5784598b8df27a43907faa0", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='False', description='fast')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0d5bf4393dbe41848098d11514894f70", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='[]', description='cell_types')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: ConnectivityPlugin\n", "Plugin: ContactPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "74c502cdcd254cb68ca22bb33bda08fd", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='energies')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "138f8d013aab4ad8af7724d147d2e688", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='1', description='neighbor_order')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: <PERSON><PERSON><PERSON><PERSON><PERSON>lugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "031a6d6ff94545709d88b565fc479f6a", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='param_specs')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cd6ce734e4eb457f943f1854143cf021", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='type_spec')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: ExternalPotentialPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "22dea1fd5a4949a399eeabf227539ad8", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='None', description='lambda_x')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1d7b5368c3654d4c84a2686631db8d51", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='None', description='lambda_y')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "552293634e834ad1a67e6b4bbcba8c3c", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='None', description='lambda_z')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ea8920cfadc74fbeb5bc33f74b5ef271", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='False', description='com_based')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2c485279e6924ed0ac654ed17eaa9471", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='param_specs')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: FocalPointPlasticityPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8f3687a5812848499f308c686cd94f67", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='1', description='neighbor_order')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "55ea9df23407456695041f46e0739ded", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='param_specs')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: LengthConstraintPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "756e9b05670f48a9aed8395ab1786f55", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='param_specs')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: MomentOfInertiaPlugin\n", "Plugin: <PERSON>eighborTrackerPlugin\n", "Plugin: PixelTrackerPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "540dfad10abf4dfb9935becfce57af93", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='False', description='track_medium')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: SecretionPlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "89d9e4d3177a45e89ba2b5c361cc7218", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='True', description='pixel_tracker')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2f3bec4ff640446886f082548f2278e8", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='True', description='boundary_pixel_tracker')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a0ddc1e72a8c49c2a823103550e2a75d", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='field_specs')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: SurfacePlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7f05e5d4fa9744068c97864cb32a03b5", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='params')"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Plugin: VolumePlugin\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3097c6f7b22a42bbb533a6991027b546", "version_major": 2, "version_minor": 0}, "text/plain": ["Text(value='{}', description='params')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import ipywidgets as widgets\n", "from IPython.display import display\n", "from cc3d.core.PyCoreSpecs import PLUGINS\n", "\n", "for plugin_cls in PLUGINS:\n", "    plugin = plugin_cls()\n", "    print(f\"Plugin: {plugin_cls.__name__}\")\n", "    for param_name in plugin.spec_dict.keys():\n", "        # For demonstration, create a text box for each parameter\n", "        value = plugin.spec_dict[param_name]\n", "        input_widget = widgets.Text(\n", "            value=str(value),\n", "            description=param_name\n", "        )\n", "        display(input_widget)"]}, {"cell_type": "code", "execution_count": 4, "id": "0d812faa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['AdhesionFlexPlugin', 'BoundaryPixelTrackerPlugin', 'CellTypePlugin', 'CenterOfMassPlugin', 'ChemotaxisPlugin', 'ConnectivityGlobalPlugin', 'ConnectivityPlugin', 'ContactPlugin', 'CurvaturePlugin', 'ExternalPotentialPlugin', 'FocalPointPlasticityPlugin', 'LengthConstraintPlugin', 'MomentOfInertiaPlugin', 'NeighborTrackerPlugin', 'PixelTrackerPlugin', 'SecretionPlugin', 'SurfacePlugin', 'VolumePlugin']\n"]}], "source": ["from cc3d.core.PyCoreSpecs import PLUGINS\n", "print([p.__name__ for p in PLUGINS])"]}, {"cell_type": "code", "execution_count": 5, "id": "a9f35f1e", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "612c777bc7374a4c924bb621b82dfd2c", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Tab(children=(VBox(children=(HTML(value='<h4>Simulation Metadata</h4>'), IntText(value=1, descr…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'specs = [\\n    Metadata(), \\n    PottsCore(dim_x=100, dim_y=100, neighbor_order=2)\\n]'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Option 2\n", "from Spec_UI import SpecificationSetupUI\n", "ui = SpecificationSetupUI()\n", "# Don't currently have a spepare Metadata class. \n", "# User configuration still in JSON? (yes) or a list like \"spec\"\n", "# Lots of constraints - initliazer email okie extracting dependacy relationships from the API\n", "'''specs = [\n", "    <PERSON><PERSON><PERSON>(), \n", "    PottsCore(dim_x=100, dim_y=100, neighbor_order=2)\n", "]'''"]}, {"cell_type": "code", "execution_count": 6, "id": "0aed6d03", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e3b62314927c4d6cb0bb6224c9438655", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(HTML(value='<h4>Potts Core Parameters</h4>'), HBox(children=(IntText(value=1, description='X Di…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from Spec_UI import PottsWidget, DEFAULTS\n", "from IPython.display import display\n", "\n", "potts_widget = PottsWidget(DEFAULTS[\"PottsCore\"])\n", "display(potts_widget.create_ui())"]}, {"cell_type": "code", "execution_count": 7, "id": "98d30699", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8ff682ae5a66463a88e17846d3f72549", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(HTML(value='<h4>Cell Types</h4>'), VBox(children=(HBox(children=(Label(value='Medium '), Button…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from Spec_UI import CellTypeWidget, DEFAULTS\n", "from IPython.display import display\n", "\n", "celltype_widget = CellTypeWidget(DEFAULTS[\"CellType\"])\n", "display(celltype_widget.create_ui())"]}, {"cell_type": "code", "execution_count": 8, "id": "0143abe3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Validation error for AdhesionFlexPlugin: Could not validate single instance of Potts (AdhesionFlex)\n", "Could not validate single instance of CellType (AdhesionFlex)\n", "Validation error for BoundaryPixelTrackerPlugin: Could not validate single instance of Potts (BoundaryPixelTracker)\n", "Could not validate single instance of CellType (BoundaryPixelTracker)\n", "Validation error for ChemotaxisPlugin: Could not validate single instance of Potts (Chemotaxis)\n", "Could not validate single instance of CellType (Chemotaxis)\n", "Validation error for ContactPlugin: Could not validate single instance of Potts (Contact)\n", "Could not validate single instance of CellType (Contact)\n", "Validation error for CurvaturePlugin: Could not validate single instance of Potts (Curvature)\n", "Could not validate single instance of CellType (Curvature)\n", "Validation error for ExternalPotentialPlugin: Could not validate single instance of Potts (ExternalPotential)\n", "Could not validate single instance of CellType (ExternalPotential)\n", "Validation error for FocalPointPlasticityPlugin: Could not validate single instance of Potts (FocalPointPlasticity)\n", "Could not validate single instance of CellType (FocalPointPlasticity)\n", "Validation error for LengthConstraintPlugin: Could not validate single instance of Potts (LengthConstraint)\n", "Could not validate single instance of CellType (LengthConstraint)\n", "Validation error for PixelTrackerPlugin: Could not validate single instance of Potts (PixelTracker)\n", "Could not validate single instance of CellType (PixelTracker)\n", "Validation error for SecretionPlugin: Could not validate single instance of Potts (Secretion)\n", "Could not validate single instance of CellType (Secretion)\n", "Could not validate single instance of PixelTracker (Secretion)\n", "Could not validate single instance of BoundaryPixelTracker (Secretion)\n", "Validation error for VolumePlugin: Could not validate single instance of Potts (Volume)\n", "Could not validate single instance of CellType (Volume)\n", "Validation error for SurfacePlugin: Could not validate single instance of Potts (Surface)\n", "Could not validate single instance of CellType (Surface)\n", "Validation error for AdhesionFlexPlugin: Could not validate single instance of Potts (AdhesionFlex)\n", "Could not validate single instance of CellType (AdhesionFlex)\n", "Validation error for BoundaryPixelTrackerPlugin: Could not validate single instance of Potts (BoundaryPixelTracker)\n", "Could not validate single instance of CellType (BoundaryPixelTracker)\n", "Validation error for ChemotaxisPlugin: Could not validate single instance of Potts (Chemotaxis)\n", "Could not validate single instance of CellType (Chemotaxis)\n", "Validation error for ContactPlugin: Could not validate single instance of Potts (Contact)\n", "Could not validate single instance of CellType (Contact)\n", "Validation error for CurvaturePlugin: Could not validate single instance of Potts (Curvature)\n", "Could not validate single instance of CellType (Curvature)\n", "Validation error for ExternalPotentialPlugin: Could not validate single instance of Potts (ExternalPotential)\n", "Could not validate single instance of CellType (ExternalPotential)\n", "Validation error for FocalPointPlasticityPlugin: Could not validate single instance of Potts (FocalPointPlasticity)\n", "Could not validate single instance of CellType (FocalPointPlasticity)\n", "Validation error for LengthConstraintPlugin: Could not validate single instance of Potts (LengthConstraint)\n", "Could not validate single instance of CellType (LengthConstraint)\n", "Validation error for PixelTrackerPlugin: Could not validate single instance of Potts (PixelTracker)\n", "Could not validate single instance of CellType (PixelTracker)\n", "Validation error for SecretionPlugin: Could not validate single instance of Potts (Secretion)\n", "Could not validate single instance of CellType (Secretion)\n", "Could not validate single instance of PixelTracker (Secretion)\n", "Could not validate single instance of BoundaryPixelTracker (Secretion)\n", "Validation error for VolumePlugin: Could not validate single instance of Potts (Volume)\n", "Could not validate single instance of CellType (Volume)\n", "Validation error for SurfacePlugin: Could not validate single instance of Potts (Surface)\n", "Could not validate single instance of CellType (Surface)\n", "Validation error for AdhesionFlexPlugin: Could not validate single instance of Potts (AdhesionFlex)\n", "Could not validate single instance of CellType (AdhesionFlex)\n", "Validation error for BoundaryPixelTrackerPlugin: Could not validate single instance of Potts (BoundaryPixelTracker)\n", "Could not validate single instance of CellType (BoundaryPixelTracker)\n", "Validation error for ChemotaxisPlugin: Could not validate single instance of Potts (Chemotaxis)\n", "Could not validate single instance of CellType (Chemotaxis)\n", "Validation error for ContactPlugin: Could not validate single instance of Potts (Contact)\n", "Could not validate single instance of CellType (Contact)\n", "Validation error for CurvaturePlugin: Could not validate single instance of Potts (Curvature)\n", "Could not validate single instance of CellType (Curvature)\n", "Validation error for ExternalPotentialPlugin: Could not validate single instance of Potts (ExternalPotential)\n", "Could not validate single instance of CellType (ExternalPotential)\n", "Validation error for FocalPointPlasticityPlugin: Could not validate single instance of Potts (FocalPointPlasticity)\n", "Could not validate single instance of CellType (FocalPointPlasticity)\n", "Validation error for LengthConstraintPlugin: Could not validate single instance of Potts (LengthConstraint)\n", "Could not validate single instance of CellType (LengthConstraint)\n", "Validation error for PixelTrackerPlugin: Could not validate single instance of Potts (PixelTracker)\n", "Could not validate single instance of CellType (PixelTracker)\n", "Validation error for SecretionPlugin: Could not validate single instance of Potts (Secretion)\n", "Could not validate single instance of CellType (Secretion)\n", "Could not validate single instance of PixelTracker (Secretion)\n", "Could not validate single instance of BoundaryPixelTracker (Secretion)\n", "Validation error for VolumePlugin: Could not validate single instance of Potts (Volume)\n", "Could not validate single instance of CellType (Volume)\n", "Validation error for SurfacePlugin: Could not validate single instance of Potts (Surface)\n", "Could not validate single instance of CellType (Surface)\n", "Validation error for AdhesionFlexPlugin: Could not validate single instance of Potts (AdhesionFlex)\n", "Could not validate single instance of CellType (AdhesionFlex)\n", "Validation error for BoundaryPixelTrackerPlugin: Could not validate single instance of Potts (BoundaryPixelTracker)\n", "Could not validate single instance of CellType (BoundaryPixelTracker)\n", "Validation error for ChemotaxisPlugin: Could not validate single instance of Potts (Chemotaxis)\n", "Could not validate single instance of CellType (Chemotaxis)\n", "Validation error for ContactPlugin: Could not validate single instance of Potts (Contact)\n", "Could not validate single instance of CellType (Contact)\n", "Validation error for CurvaturePlugin: Could not validate single instance of Potts (Curvature)\n", "Could not validate single instance of CellType (Curvature)\n", "Validation error for ExternalPotentialPlugin: Could not validate single instance of Potts (ExternalPotential)\n", "Could not validate single instance of CellType (ExternalPotential)\n", "Validation error for FocalPointPlasticityPlugin: Could not validate single instance of Potts (FocalPointPlasticity)\n", "Could not validate single instance of CellType (FocalPointPlasticity)\n", "Validation error for LengthConstraintPlugin: Could not validate single instance of Potts (LengthConstraint)\n", "Could not validate single instance of CellType (LengthConstraint)\n", "Validation error for PixelTrackerPlugin: Could not validate single instance of Potts (PixelTracker)\n", "Could not validate single instance of CellType (PixelTracker)\n", "Validation error for SecretionPlugin: Could not validate single instance of Potts (Secretion)\n", "Could not validate single instance of CellType (Secretion)\n", "Could not validate single instance of PixelTracker (Secretion)\n", "Could not validate single instance of BoundaryPixelTracker (Secretion)\n", "Validation error for VolumePlugin: Could not validate single instance of Potts (Volume)\n", "Could not validate single instance of CellType (Volume)\n", "Validation error for SurfacePlugin: Could not validate single instance of Potts (Surface)\n", "Could not validate single instance of CellType (Surface)\n"]}], "source": ["ui.save_to_json()  # Manual save\n", "ui.reset_all()     # Reset everything (pass None as required argument)\n", "config = ui.current_config()  # Get current state"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}