from Spec_UI import SpecificationSetupUI

ui = SpecificationSetupUI()

# Known Issue:
# 1. Cell Type Id assignment issue (no need manual assign)
# 2. Save configuration supose to validate/save to json (right now it's just UI)
''' 
The "Save Configuration" button might not be needed as .spec_dict validate constraint. 

'''
# 3. Plugins grouping are not fully correct | Plugins are not saved 
'''
Follow the Widzard simulation? or how should this be grouped?
Data are not stored in the json file as they suppose to --> debug file. 
(Stretch HTML goal) Also, the UI can be fixed vertical tabs, tabs in a tabs seems a little weird 
'''

# 4. Range constraint rely on defintion and widget min/max adjustment  
''' 
- Redundency if both front/back check;
- Rely more on the defition of constraint + display that text to user 
- Frontend adjustment as user can't select negative value --> min values are usually defined in the API but not max values,
max values might need to rely on defintion check. 
'''


# Restart kernel first, then run this:

# Test 1: Enhanced cell ID detection
print("🔍 Enhanced Cell ID Detection...")
cell_types_with_ids = ui.test_cell_type_ids()

# Test 2: Comprehensive cell ID search
print("\n" + "="*60)
ui.find_actual_cell_ids()

# Test 3: Quick check of current state
print("\n" + "="*60)
print("📋 Current UI State:")
ui.celltype_widget.debug_cell_types()

# %run ./Spec_UI.py IGNORE

import ipywidgets as widgets
from IPython.display import display
from cc3d.core.PyCoreSpecs import PLUGINS

for plugin_cls in PLUGINS:
    plugin = plugin_cls()
    print(f"Plugin: {plugin_cls.__name__}")
    for param_name in plugin.spec_dict.keys():
        # For demonstration, create a text box for each parameter
        value = plugin.spec_dict[param_name]
        input_widget = widgets.Text(
            value=str(value),
            description=param_name
        )
        display(input_widget)

from cc3d.core.PyCoreSpecs import PLUGINS
print([p.__name__ for p in PLUGINS])

# Option 2
from Spec_UI import SpecificationSetupUI
ui = SpecificationSetupUI()
# Don't currently have a spepare Metadata class. 
# User configuration still in JSON? (yes) or a list like "spec"
# Lots of constraints - initliazer email okie extracting dependacy relationships from the API
'''specs = [
    Metadata(), 
    PottsCore(dim_x=100, dim_y=100, neighbor_order=2)
]'''

from Spec_UI import PottsWidget, DEFAULTS
from IPython.display import display

potts_widget = PottsWidget(DEFAULTS["PottsCore"])
display(potts_widget.create_ui())

from Spec_UI import CellTypeWidget, DEFAULTS
from IPython.display import display

celltype_widget = CellTypeWidget(DEFAULTS["CellType"])
display(celltype_widget.create_ui())

ui.save_to_json()  # Manual save
ui.reset_all()     # Reset everything (pass None as required argument)
config = ui.current_config()  # Get current state